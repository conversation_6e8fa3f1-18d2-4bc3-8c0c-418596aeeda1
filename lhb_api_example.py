#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
实时龙虎榜列表 API 使用示例
"""

from yapi_generated_code import LhbListAPI, AsyncLhbListAPI
import asyncio


def main():
    """主函数 - 同步版本示例"""
    print("=== 实时龙虎榜列表 API 调用示例 ===\n")
    
    # 创建API客户端
    api = LhbListAPI(base_url="http://192.168.35.17:3000")
    
    # 如果需要认证，可以设置token
    # api.set_auth_token("your_token_here")
    
    # 如果需要自定义请求头
    # api.set_headers({
    #     'User-Agent': 'LhbApp/1.0',
    #     'X-Device-ID': 'device_123'
    # })
    
    try:
        print("正在获取龙虎榜数据...")
        
        # 调用API获取数据
        response = api.get_lhb_list()
        
        print(f"✅ 请求成功!")
        print(f"HTTP状态码: {response.httpCode}")
        print(f"响应消息: {response.msg}")
        print(f"时间戳: {response.timestamp}")
        print(f"获取到 {len(response.data)} 条龙虎榜数据\n")
        
        # 显示前3条数据详情
        print("📊 前3条龙虎榜数据:")
        print("-" * 80)
        
        for i, stock in enumerate(response.data[:3], 1):
            print(f"第{i}条:")
            print(f"  📈 股票: {stock.stockCode} - {stock.stockName}")
            print(f"  💰 现价: {stock.lastPx}")
            print(f"  📊 涨跌幅: {stock.pxChangeRate}%")
            print(f"  💵 成交额: {stock.businessBalance}")
            print(f"  🏢 板块: {stock.block}")
            print(f"  🔄 换手率: {stock.turnoverRatio}%")
            print(f"  📈 振幅: {stock.amplitude}%")
            if stock.limitUpReason:
                print(f"  🚀 涨停原因: {stock.limitUpReason}")
            if stock.daysLimitUp:
                print(f"  📅 连板情况: {stock.daysLimitUp}")
            print()
        
        # 统计信息
        print("📈 统计信息:")
        print("-" * 40)
        
        # 计算涨停股票数量
        limit_up_count = sum(1 for stock in response.data if stock.pxChangeRate and stock.pxChangeRate >= 9.9)
        print(f"涨停股票数量: {limit_up_count}")
        
        # 计算平均涨跌幅
        valid_changes = [stock.pxChangeRate for stock in response.data if stock.pxChangeRate is not None]
        if valid_changes:
            avg_change = sum(valid_changes) / len(valid_changes)
            print(f"平均涨跌幅: {avg_change:.2f}%")
        
        # 板块分布
        blocks = {}
        for stock in response.data:
            if stock.block:
                blocks[stock.block] = blocks.get(stock.block, 0) + 1
        
        print(f"涉及板块数量: {len(blocks)}")
        if blocks:
            top_blocks = sorted(blocks.items(), key=lambda x: x[1], reverse=True)[:5]
            print("热门板块TOP5:")
            for block, count in top_blocks:
                print(f"  {block}: {count}只")
                
    except Exception as e:
        print(f"❌ 获取数据失败: {e}")


async def async_main():
    """异步版本示例"""
    print("\n=== 异步版本示例 ===\n")
    
    # 创建异步API客户端
    async_api = AsyncLhbListAPI(base_url="http://192.168.35.17:3000")
    
    try:
        print("正在异步获取龙虎榜数据...")
        
        # 异步调用API
        response = await async_api.get_lhb_list()
        
        print(f"✅ 异步请求成功!")
        print(f"获取到 {len(response.data)} 条龙虎榜数据")
        
        # 显示涨幅最大的3只股票
        sorted_stocks = sorted(
            [stock for stock in response.data if stock.pxChangeRate is not None],
            key=lambda x: x.pxChangeRate,
            reverse=True
        )
        
        print("\n🚀 涨幅最大的3只股票:")
        print("-" * 50)
        
        for i, stock in enumerate(sorted_stocks[:3], 1):
            print(f"第{i}名: {stock.stockName}({stock.stockCode}) "
                  f"涨幅: {stock.pxChangeRate}%")
        
    except Exception as e:
        print(f"❌ 异步获取数据失败: {e}")


def test_raw_api():
    """测试原始数据API"""
    print("\n=== 原始数据API测试 ===\n")
    
    api = LhbListAPI()
    
    try:
        # 获取原始JSON数据
        raw_data = api.get_lhb_list_raw()
        
        print("✅ 原始数据获取成功!")
        print(f"数据类型: {type(raw_data)}")
        print(f"数据键: {list(raw_data.keys())}")
        
        if 'data' in raw_data and raw_data['data']:
            print(f"数据条数: {len(raw_data['data'])}")
            print("第一条数据的键:", list(raw_data['data'][0].keys()))
        
    except Exception as e:
        print(f"❌ 获取原始数据失败: {e}")


if __name__ == "__main__":
    # 运行同步版本
    main()
    
    # 运行异步版本
    asyncio.run(async_main())
    
    # 测试原始数据API
    test_raw_api() 