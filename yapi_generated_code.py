#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
实时龙虎榜列表 API 请求方法和数据模型
接口ID: 104693
接口路径: /api/hq/app/lhb/list
请求方式: POST
"""

import requests
from typing import List, Optional
from dataclasses import dataclass
from datetime import datetime
import json


@dataclass
class StockLhbItem:
    """龙虎榜股票项数据模型"""
    stockCode: str  # 股票编码
    stockName: str  # 股票名称
    businessAmount: Optional[float] = None  # 竞价量 (此字段列表不展示)
    businessAmountRatio: Optional[float] = None  # 竞价量比
    businessBalance: Optional[float] = None  # 竞价金额（成交额）
    businessBalanceRatio: Optional[float] = None  # 竞价涨幅
    popularity: Optional[float] = None  # 人气值 (先做埋点，不展示)
    block: Optional[str] = None  # 板块
    intervalRatio: Optional[float] = None  # 区间涨幅
    realTimeFlowStocks: Optional[float] = None  # 实时流通股数 (此字段列表不展示，每日盘前更新)
    realTimeFlowAmount: Optional[float] = None  # 实际流通
    sellFlowRatio: Optional[float] = None  # 卖流占比
    netFlowRatio: Optional[float] = None  # 净流占比
    firstIncrease: Optional[float] = None  # 第一季度机构增仓
    limitUpReason: Optional[str] = None  # 涨停原因
    daysLimitUp: Optional[str] = None  # 几天几板
    lastPx: Optional[float] = None  # 现价
    pxChangeRate: Optional[float] = None  # 涨跌幅
    mainNetInflow: Optional[float] = None  # 主力流入
    mainNetOutflow: Optional[float] = None  # 主力流出
    mainNetNetInflow: Optional[float] = None  # 主力净流入
    circulationValue: Optional[float] = None  # 流通市值
    marketValue: Optional[float] = None  # 证券市值（总市值）
    volRatio: Optional[float] = None  # 量比
    turnoverRatio: Optional[float] = None  # 换手率
    amplitude: Optional[float] = None  # 振幅
    dUpSpeed: Optional[float] = None  # 涨速


@dataclass
class LhbListResponse:
    """龙虎榜列表响应数据模型"""
    httpCode: int  # HTTP状态码
    msg: str  # 响应消息
    token: str  # 令牌
    timestamp: int  # 时间戳
    data: List[StockLhbItem]  # 龙虎榜数据列表


class LhbListAPI:
    """实时龙虎榜列表API客户端"""
    
    def __init__(self, base_url: str = "http://*************:3000"):
        """
        初始化API客户端
        
        Args:
            base_url: API基础URL
        """
        self.base_url = base_url.rstrip('/')
        self.session = requests.Session()
        
    def set_headers(self, headers: dict):
        """设置请求头"""
        self.session.headers.update(headers)
        
    def set_auth_token(self, token: str):
        """设置认证令牌"""
        self.session.headers.update({
            'Authorization': f'Bearer {token}',
            'Content-Type': 'application/json'
        })
    
    def get_lhb_list(self, **kwargs) -> LhbListResponse:
        """
        获取实时龙虎榜列表
        
        Args:
            **kwargs: 额外的请求参数（根据实际需要添加）
            
        Returns:
            LhbListResponse: 龙虎榜列表响应数据
            
        Raises:
            requests.RequestException: 请求异常
            ValueError: 数据解析异常
        """
        url = f"{self.base_url}/api/hq/app/lhb/list"
        
        try:
            # 发送POST请求
            response = self.session.post(url, json=kwargs, timeout=30)
            response.raise_for_status()
            
            # 解析响应数据
            data = response.json()
            
            # 构建响应对象
            stock_items = []
            if data.get('data'):
                for item in data['data']:
                    stock_items.append(StockLhbItem(**item))
            
            return LhbListResponse(
                httpCode=data.get('httpCode', 0),
                msg=data.get('msg', ''),
                token=data.get('token', ''),
                timestamp=data.get('timestamp', 0),
                data=stock_items
            )
            
        except requests.RequestException as e:
            raise requests.RequestException(f"请求失败: {e}")
        except (KeyError, TypeError, ValueError) as e:
            raise ValueError(f"数据解析失败: {e}")
    
    def get_lhb_list_raw(self, **kwargs) -> dict:
        """
        获取实时龙虎榜列表（原始数据）
        
        Args:
            **kwargs: 额外的请求参数
            
        Returns:
            dict: 原始响应数据
        """
        url = f"{self.base_url}/api/hq/app/lhb/list"
        
        response = self.session.post(url, json=kwargs, timeout=30)
        response.raise_for_status()
        
        return response.json()


# 使用示例
def example_usage():
    """使用示例"""
    # 创建API客户端
    api_client = LhbListAPI()
    
    # 设置认证信息（如果需要）
    # api_client.set_auth_token("your_token_here")
    
    # 设置自定义请求头（如果需要）
    # api_client.set_headers({
    #     'User-Agent': 'Your App/1.0',
    #     'X-Custom-Header': 'custom_value'
    # })
    
    try:
        # 获取龙虎榜列表
        result = api_client.get_lhb_list()
        
        print(f"HTTP状态码: {result.httpCode}")
        print(f"响应消息: {result.msg}")
        print(f"数据条数: {len(result.data)}")
        
        # 打印前5条数据
        for i, stock in enumerate(result.data[:5]):
            print(f"\n第{i+1}条:")
            print(f"  股票代码: {stock.stockCode}")
            print(f"  股票名称: {stock.stockName}")
            print(f"  现价: {stock.lastPx}")
            print(f"  涨跌幅: {stock.pxChangeRate}%")
            print(f"  成交额: {stock.businessBalance}")
            print(f"  板块: {stock.block}")
            
    except Exception as e:
        print(f"获取数据失败: {e}")


# 异步版本（可选）
import asyncio
import aiohttp
from typing import AsyncGenerator


class AsyncLhbListAPI:
    """异步版本的龙虎榜列表API客户端"""
    
    def __init__(self, base_url: str = "http://*************:3000"):
        self.base_url = base_url.rstrip('/')
        self.headers = {}
    
    def set_auth_token(self, token: str):
        """设置认证令牌"""
        self.headers.update({
            'Authorization': f'Bearer {token}',
            'Content-Type': 'application/json'
        })
    
    async def get_lhb_list(self, **kwargs) -> LhbListResponse:
        """异步获取龙虎榜列表"""
        url = f"{self.base_url}/api/hq/app/lhb/list"
        
        async with aiohttp.ClientSession(headers=self.headers) as session:
            async with session.post(url, json=kwargs) as response:
                response.raise_for_status()
                data = await response.json()
                
                stock_items = []
                if data.get('data'):
                    for item in data['data']:
                        stock_items.append(StockLhbItem(**item))
                
                return LhbListResponse(
                    httpCode=data.get('httpCode', 0),
                    msg=data.get('msg', ''),
                    token=data.get('token', ''),
                    timestamp=data.get('timestamp', 0),
                    data=stock_items
                )


# 异步使用示例
async def async_example_usage():
    """异步使用示例"""
    api_client = AsyncLhbListAPI()
    
    try:
        result = await api_client.get_lhb_list()
        print(f"异步获取到 {len(result.data)} 条龙虎榜数据")
        
    except Exception as e:
        print(f"异步获取数据失败: {e}")


if __name__ == "__main__":
    # 运行同步示例
    print("=== 同步版本示例 ===")
    example_usage()
    
    # 运行异步示例
    print("\n=== 异步版本示例 ===")
    asyncio.run(async_example_usage()) 